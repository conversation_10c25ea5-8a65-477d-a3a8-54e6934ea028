package com.taobao.wireless.orange.publish.probe;

import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.common.constant.enums.AserverIndexType;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.external.switchcenter.SwitchConfig;
import com.taobao.wireless.orange.external.wmcc.WmccPublishService;
import com.taobao.wireless.orange.text.dal.dao.ProbeDAO;
import com.taobao.wireless.orange.text.dal.dao.ProbeTaskDAO;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskDO;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskExt;
import com.taobao.wmcc.client.constants.BriefTaskStatus;
import com.taobao.wmcc.client.publish.ConfigPublishRequest;
import com.taobao.wmcc.client.publish.PublishTaskInfo;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TextProbePublishService 单元测试
 *
 * 主要测试场景：
 * 1. 紧急任务和非紧急任务在有进行中发布单情况下的处理逻辑
 * 2. 任务取消和补偿机制
 * 3. 探针数据生成和发布流程
 *
 * <AUTHOR>
 */
public class TextProbePublishServiceTest extends BaseTest {

    private TextProbePublishService textProbePublishService;

    @Mock
    private ProbeTaskDAO probeTaskDAO;

    @Mock
    private ProbeDAO probeDAO;

    @Mock
    private WmccPublishService wmccPublishService;

    // 测试常量
    private static final String TEST_APP_KEY_EMERGENCY = "emergency_app";
    private static final String TEST_APP_KEY_NORMAL = "normal_app";
    private static final String TEST_CDN_DOMAIN = "test.cdn.domain.com";
    private static final Long TEST_RUNNING_TASK_ID = 12345L;
    private static final Long TEST_NEW_TASK_ID = 67890L;
    private static final String TEST_INDEX_VERSION = "1120250519203202012";
    private static final String TEST_METAS = "[{\"baseVersion\":\"1120250519193601077\",\"resourceId\":\"test-resource-id\",\"md5\":\"test-md5\"}]";

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        textProbePublishService = new TextProbePublishService();
        ReflectionTestUtils.setField(textProbePublishService, "probeTaskDAO", probeTaskDAO);
        ReflectionTestUtils.setField(textProbePublishService, "probeDAO", probeDAO);
        ReflectionTestUtils.setField(textProbePublishService, "wmccPublishService", wmccPublishService);
        ReflectionTestUtils.setField(textProbePublishService, "cdnDomain", TEST_CDN_DOMAIN);

        // Mock SwitchConfig
        SwitchConfig.forcePublishTextProbeAppKeys = new ArrayList<>();
    }

    /**
     * 测试紧急任务在有进行中发布单时的处理逻辑
     * 预期：取消现有任务，然后发布新任务
     */
    @Test
    public void testPublish_EmergencyTaskWithRunningPublish_ShouldCancelAndPublish() {
        // 准备候选应用数据 - 紧急应用
        List<ProbeTaskDO> candidateTasks = Arrays.asList(
                createProbeTaskDO(TEST_APP_KEY_EMERGENCY, Emergent.y)
        );
        when(probeTaskDAO.getAgatewareCandidateProbeTasks()).thenReturn(candidateTasks);

        // Mock 有正在运行的任务
        PublishTaskInfo runningTask = new PublishTaskInfo();
        runningTask.setTaskId(TEST_RUNNING_TASK_ID);
        runningTask.setStatus(BriefTaskStatus.RUNNING);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_EMERGENCY, AserverIndexType.DP.getCode()))
                .thenReturn(runningTask);

        // Mock 探针数据
        List<ProbeTaskExt> probeData = Arrays.asList(
                createProbeTaskExt(TEST_APP_KEY_EMERGENCY)
        );
        when(probeDAO.getAvailableProbesWithMaxTaskID(anyList())).thenReturn(probeData);

        // Mock 发布新任务
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_NEW_TASK_ID);

        // 执行测试
        textProbePublishService.publish();

        // 验证取消了现有任务
        verify(wmccPublishService).cancelTask(TEST_RUNNING_TASK_ID);

        // 验证清除了被取消任务的ID
        verify(probeTaskDAO).lambdaUpdate();

        // 验证发布了新任务
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));

        // 验证更新了探针任务ID
        verify(probeTaskDAO, times(2)).lambdaUpdate(); // 一次清除，一次更新
    }

    /**
     * 测试非紧急任务在有进行中发布单时的处理逻辑
     * 预期：跳过发布，不进行任何操作
     */
    @Test
    public void testPublish_NormalTaskWithRunningPublish_ShouldSkipPublish() {
        // 准备候选应用数据 - 非紧急应用
        List<ProbeTaskDO> candidateTasks = Arrays.asList(
                createProbeTaskDO(TEST_APP_KEY_NORMAL, Emergent.n)
        );
        when(probeTaskDAO.getAgatewareCandidateProbeTasks()).thenReturn(candidateTasks);

        // Mock 有正在运行的任务
        PublishTaskInfo runningTask = new PublishTaskInfo();
        runningTask.setTaskId(TEST_RUNNING_TASK_ID);
        runningTask.setStatus(BriefTaskStatus.RUNNING);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_NORMAL, AserverIndexType.DP.getCode()))
                .thenReturn(runningTask);

        // 执行测试
        textProbePublishService.publish();

        // 验证没有取消任务
        verify(wmccPublishService, never()).cancelTask(anyLong());

        // 验证没有发布新任务
        verify(wmccPublishService, never()).publishProbeToAserver(any(), any());

        // 验证没有更新探针任务ID
        verify(probeTaskDAO, never()).lambdaUpdate();
    }

    /**
     * 测试混合场景：紧急任务和非紧急任务都有进行中的发布单
     * 预期：紧急任务取消并重新发布，非紧急任务跳过
     */
    @Test
    public void testPublish_MixedTasksWithRunningPublish_ShouldHandleCorrectly() {
        // 准备候选应用数据 - 包含紧急和非紧急应用
        List<ProbeTaskDO> candidateTasks = Arrays.asList(
                createProbeTaskDO(TEST_APP_KEY_EMERGENCY, Emergent.y),
                createProbeTaskDO(TEST_APP_KEY_NORMAL, Emergent.n)
        );
        when(probeTaskDAO.getAgatewareCandidateProbeTasks()).thenReturn(candidateTasks);

        // Mock 两个应用都有正在运行的任务
        PublishTaskInfo emergencyRunningTask = new PublishTaskInfo();
        emergencyRunningTask.setTaskId(TEST_RUNNING_TASK_ID);
        emergencyRunningTask.setStatus(BriefTaskStatus.RUNNING);

        PublishTaskInfo normalRunningTask = new PublishTaskInfo();
        normalRunningTask.setTaskId(TEST_RUNNING_TASK_ID + 1);
        normalRunningTask.setStatus(BriefTaskStatus.RUNNING);

        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_EMERGENCY, AserverIndexType.DP.getCode()))
                .thenReturn(emergencyRunningTask);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_NORMAL, AserverIndexType.DP.getCode()))
                .thenReturn(normalRunningTask);

        // Mock 探针数据 - 只有紧急应用的探针数据会被使用
        List<ProbeTaskExt> probeData = Arrays.asList(
                createProbeTaskExt(TEST_APP_KEY_EMERGENCY)
        );
        when(probeDAO.getAvailableProbesWithMaxTaskID(anyList())).thenReturn(probeData);

        // Mock 发布新任务
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_NEW_TASK_ID);

        // 执行测试
        textProbePublishService.publish();

        // 验证只取消了紧急任务
        verify(wmccPublishService).cancelTask(TEST_RUNNING_TASK_ID);
        verify(wmccPublishService, never()).cancelTask(TEST_RUNNING_TASK_ID + 1);

        // 验证只发布了紧急任务
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));
        verify(wmccPublishService, never()).publishProbeToAserver(any(), eq(Emergent.n));
    }

    /**
     * 测试失败状态的任务也会被取消
     * 预期：FAILURE状态的任务也应该被取消并重新发布
     */
    @Test
    public void testPublish_EmergencyTaskWithFailedPublish_ShouldCancelAndPublish() {
        // 准备候选应用数据 - 紧急应用
        List<ProbeTaskDO> candidateTasks = Arrays.asList(
                createProbeTaskDO(TEST_APP_KEY_EMERGENCY, Emergent.y)
        );
        when(probeTaskDAO.getAgatewareCandidateProbeTasks()).thenReturn(candidateTasks);

        // Mock 有失败状态的任务
        PublishTaskInfo failedTask = new PublishTaskInfo();
        failedTask.setTaskId(TEST_RUNNING_TASK_ID);
        failedTask.setStatus(BriefTaskStatus.FAILURE);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_EMERGENCY, AserverIndexType.DP.getCode()))
                .thenReturn(failedTask);

        // Mock 探针数据
        List<ProbeTaskExt> probeData = Arrays.asList(
                createProbeTaskExt(TEST_APP_KEY_EMERGENCY)
        );
        when(probeDAO.getAvailableProbesWithMaxTaskID(anyList())).thenReturn(probeData);

        // Mock 发布新任务
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_NEW_TASK_ID);

        // 执行测试
        textProbePublishService.publish();

        // 验证取消了失败的任务
        verify(wmccPublishService).cancelTask(TEST_RUNNING_TASK_ID);

        // 验证发布了新任务
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));
    }

    /**
     * 测试没有进行中任务时的正常发布流程
     */
    @Test
    public void testPublish_NoRunningTask_ShouldPublishNormally() {
        // 准备候选应用数据
        List<ProbeTaskDO> candidateTasks = Arrays.asList(
                createProbeTaskDO(TEST_APP_KEY_EMERGENCY, Emergent.y),
                createProbeTaskDO(TEST_APP_KEY_NORMAL, Emergent.n)
        );
        when(probeTaskDAO.getAgatewareCandidateProbeTasks()).thenReturn(candidateTasks);

        // Mock 没有正在运行的任务
        when(wmccPublishService.getRunningPublishTaskInfo(anyString(), anyString())).thenReturn(null);

        // Mock 探针数据
        List<ProbeTaskExt> probeData = Arrays.asList(
                createProbeTaskExt(TEST_APP_KEY_EMERGENCY),
                createProbeTaskExt(TEST_APP_KEY_NORMAL)
        );
        when(probeDAO.getAvailableProbesWithMaxTaskID(anyList())).thenReturn(probeData);

        // Mock 发布任务
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_NEW_TASK_ID);
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.n))).thenReturn(TEST_NEW_TASK_ID + 1);

        // 执行测试
        textProbePublishService.publish();

        // 验证没有取消任务
        verify(wmccPublishService, never()).cancelTask(anyLong());

        // 验证发布了两个任务
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.n));
    }

    /**
     * 测试WMCC配置生成的正确性
     */
    @Test
    public void testPublish_WmccConfigGeneration_ShouldGenerateCorrectFormat() {
        // 准备测试数据
        List<ProbeTaskDO> candidateTasks = Arrays.asList(
                createProbeTaskDO(TEST_APP_KEY_EMERGENCY, Emergent.y)
        );
        when(probeTaskDAO.getAgatewareCandidateProbeTasks()).thenReturn(candidateTasks);
        when(wmccPublishService.getRunningPublishTaskInfo(anyString(), anyString())).thenReturn(null);

        List<ProbeTaskExt> probeData = Arrays.asList(
                createProbeTaskExt(TEST_APP_KEY_EMERGENCY)
        );
        when(probeDAO.getAvailableProbesWithMaxTaskID(anyList())).thenReturn(probeData);
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_NEW_TASK_ID);

        // 执行测试
        textProbePublishService.publish();

        // 捕获WMCC配置参数
        ArgumentCaptor<Collection<ConfigPublishRequest.Pair<String, String>>> configCaptor =
                ArgumentCaptor.forClass(Collection.class);
        verify(wmccPublishService).publishProbeToAserver(configCaptor.capture(), eq(Emergent.y));

        // 验证配置格式
        Collection<ConfigPublishRequest.Pair<String, String>> configs = configCaptor.getValue();
        assertThat(configs).hasSize(1);

        ConfigPublishRequest.Pair<String, String> config = configs.iterator().next();
        assertThat(config.getLeft()).isEqualTo(TEST_APP_KEY_EMERGENCY + "_" + AserverIndexType.DP.getCode());
        assertThat(config.getRight()).contains(TEST_CDN_DOMAIN);
        assertThat(config.getRight()).contains(TEST_APP_KEY_EMERGENCY);
        assertThat(config.getRight()).contains(TEST_INDEX_VERSION);
    }

    // ==================== 辅助方法 ====================

    private ProbeTaskDO createProbeTaskDO(String appKey, Emergent emergent) {
        ProbeTaskDO probeTask = new ProbeTaskDO();
        probeTask.setAppKey(appKey);
        probeTask.setIsEmergent(emergent);
        return probeTask;
    }

    private ProbeTaskExt createProbeTaskExt(String appKey) {
        ProbeTaskExt probeExt = new ProbeTaskExt();
        probeExt.setAppKey(appKey);
        probeExt.setIndexVersion(TEST_INDEX_VERSION);
        probeExt.setMetas(TEST_METAS);
        probeExt.setMaxTaskId(100L);
        probeExt.setChangeVersion("1120250519203202012");
        probeExt.setPublishType("GRAY");
        return probeExt;
    }

    /**
     * 测试强制发布应用列表的处理
     * 预期：强制发布的应用会被标记为紧急，并覆盖原有设置
     */
    @Test
    public void testPublish_ForcePublishApps_ShouldMarkAsEmergency() {
        // 设置强制发布应用列表
        SwitchConfig.forcePublishTextProbeAppKeys = Arrays.asList(TEST_APP_KEY_NORMAL);

        // 准备候选应用数据 - 原本是非紧急应用
        List<ProbeTaskDO> candidateTasks = Arrays.asList(
                createProbeTaskDO(TEST_APP_KEY_NORMAL, Emergent.n)
        );
        when(probeTaskDAO.getAgatewareCandidateProbeTasks()).thenReturn(candidateTasks);
        when(wmccPublishService.getRunningPublishTaskInfo(anyString(), anyString())).thenReturn(null);

        // Mock 探针数据
        List<ProbeTaskExt> probeData = Arrays.asList(
                createProbeTaskExt(TEST_APP_KEY_NORMAL)
        );
        when(probeDAO.getAvailableProbesWithMaxTaskID(anyList())).thenReturn(probeData);
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_NEW_TASK_ID);

        // 执行测试
        textProbePublishService.publish();

        // 验证以紧急模式发布
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));
        verify(wmccPublishService, never()).publishProbeToAserver(any(), eq(Emergent.n));
    }

    /**
     * 测试探针数据选择逻辑 - 相同changeVersion时优先选择GRAY类型
     */
    @Test
    public void testGenerateProbeData_SameChangeVersion_ShouldPreferGrayType() {
        // 准备测试数据 - 相同changeVersion，不同publishType
        ProbeTaskExt probe1 = createProbeTaskExt(TEST_APP_KEY_EMERGENCY);
        probe1.setPublishType("NORMAL");
        probe1.setChangeVersion("1120250519203202012");

        ProbeTaskExt probe2 = createProbeTaskExt(TEST_APP_KEY_EMERGENCY);
        probe2.setPublishType("GRAY");
        probe2.setChangeVersion("1120250519203202012");

        List<ProbeTaskExt> probeData = Arrays.asList(probe1, probe2);
        when(probeDAO.getAvailableProbesWithMaxTaskID(anyList())).thenReturn(probeData);

        // 调用方法
        Map<String, ProbeTaskExt> result = textProbePublishService.generateProbeData(
                Set.of(TEST_APP_KEY_EMERGENCY));

        // 验证选择了GRAY类型的探针
        assertThat(result).hasSize(1);
        assertThat(result.get(TEST_APP_KEY_EMERGENCY).getPublishType()).isEqualTo("GRAY");
    }

    /**
     * 测试探针数据选择逻辑 - 不同changeVersion时选择较大版本
     */
    @Test
    public void testGenerateProbeData_DifferentChangeVersion_ShouldSelectLargerVersion() {
        // 准备测试数据 - 不同changeVersion
        ProbeTaskExt probe1 = createProbeTaskExt(TEST_APP_KEY_EMERGENCY);
        probe1.setChangeVersion("1120250519203202011");

        ProbeTaskExt probe2 = createProbeTaskExt(TEST_APP_KEY_EMERGENCY);
        probe2.setChangeVersion("1120250519203202012");

        List<ProbeTaskExt> probeData = Arrays.asList(probe1, probe2);
        when(probeDAO.getAvailableProbesWithMaxTaskID(anyList())).thenReturn(probeData);

        // 调用方法
        Map<String, ProbeTaskExt> result = textProbePublishService.generateProbeData(
                Set.of(TEST_APP_KEY_EMERGENCY));

        // 验证选择了较大版本的探针
        assertThat(result).hasSize(1);
        assertThat(result.get(TEST_APP_KEY_EMERGENCY).getChangeVersion()).isEqualTo("1120250519203202012");
    }

    /**
     * 测试空候选应用列表的处理
     * 预期：直接返回，不进行任何操作
     */
    @Test
    public void testPublish_EmptyCandidateApps_ShouldReturnDirectly() {
        // Mock 空的候选应用列表
        when(probeTaskDAO.getAgatewareCandidateProbeTasks()).thenReturn(Collections.emptyList());

        // 执行测试
        textProbePublishService.publish();

        // 验证没有进行任何WMCC操作
        verify(wmccPublishService, never()).getRunningPublishTaskInfo(anyString(), anyString());
        verify(wmccPublishService, never()).cancelTask(anyLong());
        verify(wmccPublishService, never()).publishProbeToAserver(any(), any());
    }

    /**
     * 测试发布异常处理
     * 预期：异常被捕获，不影响其他应用的发布
     */
    @Test
    public void testPublish_PublishException_ShouldContinueWithOtherApps() {
        // 准备候选应用数据
        List<ProbeTaskDO> candidateTasks = Arrays.asList(
                createProbeTaskDO(TEST_APP_KEY_EMERGENCY, Emergent.y),
                createProbeTaskDO(TEST_APP_KEY_NORMAL, Emergent.n)
        );
        when(probeTaskDAO.getAgatewareCandidateProbeTasks()).thenReturn(candidateTasks);
        when(wmccPublishService.getRunningPublishTaskInfo(anyString(), anyString())).thenReturn(null);

        // Mock 探针数据
        List<ProbeTaskExt> probeData = Arrays.asList(
                createProbeTaskExt(TEST_APP_KEY_EMERGENCY),
                createProbeTaskExt(TEST_APP_KEY_NORMAL)
        );
        when(probeDAO.getAvailableProbesWithMaxTaskID(anyList())).thenReturn(probeData);

        // Mock 第一个应用发布失败，第二个成功
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y)))
                .thenThrow(new RuntimeException("发布失败"));
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.n)))
                .thenReturn(TEST_NEW_TASK_ID);

        // 执行测试
        textProbePublishService.publish();

        // 验证两个应用都尝试发布
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.n));

        // 验证只有成功的应用更新了任务ID
        verify(probeTaskDAO, times(1)).lambdaUpdate();
    }

    /**
     * 测试updateProbeTaskId方法的正确性
     */
    @Test
    public void testUpdateProbeTaskId_ShouldUpdateCorrectRecords() {
        ProbeTaskExt probe = createProbeTaskExt(TEST_APP_KEY_EMERGENCY);
        probe.setMaxTaskId(100L);

        // 执行测试
        textProbePublishService.updateProbeTaskId(probe, TEST_NEW_TASK_ID);

        // 验证调用了正确的更新方法
        verify(probeTaskDAO).lambdaUpdate();
    }

    /**
     * 测试clearCanceledProbeTaskId方法的正确性
     */
    @Test
    public void testClearCanceledProbeTaskId_ShouldClearCorrectRecords() {
        // 执行测试
        textProbePublishService.clearCanceledProbeTaskId(TEST_APP_KEY_EMERGENCY, TEST_RUNNING_TASK_ID);

        // 验证调用了正确的更新方法
        verify(probeTaskDAO).lambdaUpdate();
    }

    /**
     * 测试getIndexType方法
     */
    @Test
    public void testGetIndexType_ShouldReturnDP() {
        AserverIndexType result = textProbePublishService.getIndexType();
        assertThat(result).isEqualTo(AserverIndexType.DP);
    }

    /**
     * 测试getCdnDomain方法
     */
    @Test
    public void testGetCdnDomain_ShouldReturnConfiguredDomain() {
        String result = textProbePublishService.getCdnDomain();
        assertThat(result).isEqualTo(TEST_CDN_DOMAIN);
    }
}
